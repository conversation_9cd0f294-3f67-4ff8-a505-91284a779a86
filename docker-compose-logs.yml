---
# Standardized Docker Compose for Log Management
# Uses host network to scan all Docker containers automatically
# Displays logs grouped by project name with file output

version: '3.8'

services:
  # Dozzle - Universal Container Log Viewer
  # Automatically scans ALL Docker containers on the host
  dozzle:
    image: amir20/dozzle:latest
    container_name: universal-dozzle
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host  # Use host network for automatic container discovery
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/hosts:/etc/hosts:ro
      - ./logs:/logs  # Mount local logs directory
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_NO_ANALYTICS=true
      - DOZZLE_HOSTNAME=${HOSTNAME:-docker-host}
      - DOZZLE_ENABLE_ACTIONS=true
      - DOZZLE_ADDR=0.0.0.0:8080  # Bind to all interfaces
      # Group containers by project labels automatically
      - DOZZLE_FILTER=""  # Empty filter to show ALL containers
    ports:
      - "8080:8080"  # Dozzle web interface
    mem_limit: 200m
    cpus: 0.5
    labels:
      - "com.docker.compose.project=logs"
      - "description=Universal Docker container log viewer"

  # Log Aggregator - Collects logs from all containers to files
  # This service writes container logs to files for persistent storage
  log-collector:
    image: alpine:latest
    container_name: log-collector
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/logs
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    environment:
      - TZ=Asia/Kathmandu
    command: |
      sh -c '
        echo "🚀 Starting Universal Log Collector..."
        
        # Install required packages
        apk add --no-cache docker-cli jq curl
        
        # Create logs directory structure
        mkdir -p /logs/containers
        mkdir -p /logs/projects
        
        # Function to collect logs from all containers
        collect_logs() {
          echo "📊 Scanning for Docker containers..."
          
          # Get all running containers
          docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" > /logs/container_status.txt
          
          # Collect logs for each container
          docker ps --format "{{.Names}}" | while read container_name; do
            if [ ! -z "$container_name" ]; then
              echo "📝 Collecting logs for: $container_name"
              
              # Get container labels to determine project
              project_name=$(docker inspect "$container_name" --format "{{index .Config.Labels \"com.docker.compose.project\"}}" 2>/dev/null || echo "unknown")
              
              # Create project directory if it does not exist
              mkdir -p "/logs/projects/$project_name"
              
              # Collect recent logs (last 1000 lines)
              docker logs --tail=1000 "$container_name" > "/logs/containers/${container_name}.log" 2>&1
              
              # Also copy to project directory
              cp "/logs/containers/${container_name}.log" "/logs/projects/$project_name/${container_name}.log"
              
              echo "✅ Logs collected for $container_name (project: $project_name)"
            fi
          done
          
          echo "📈 Log collection completed at $(date)"
          echo "---" >> /logs/collection_history.txt
          echo "$(date): Collected logs for $(docker ps --format {{.Names}} | wc -l) containers" >> /logs/collection_history.txt
        }
        
        # Initial log collection
        collect_logs
        
        # Set up periodic log collection (every 5 minutes)
        while true; do
          sleep 300  # 5 minutes
          collect_logs
        done
      '
    labels:
      - "com.docker.compose.project=logs"
      - "description=Automatic log collector for all Docker containers"

# No custom networks needed - using host network for automatic discovery
