---
# Universal Dozzle and Log Management
# Auto-discovers ALL Docker containers with host network
# Groups by project names automatically

version: '3.8'

services:
  # Universal Dozzle - Scans ALL containers automatically
  dozzle:
    image: amir20/dozzle:latest
    container_name: universal-dozzle
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host  # Host network for auto-discovery
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/hosts:/etc/hosts:ro
      - ./logs:/logs
    ports:
      - "8203:8080"
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_NO_ANALYTICS=true
      - DOZZLE_HOSTNAME=${HOSTNAME:-docker-host}
      - DOZZLE_ENABLE_ACTIONS=true
      - DOZZLE_ADDR=0.0.0.0:8080
      - DOZZLE_TAILSIZE=1000
      # Group by Docker Compose project (directory-based)
      - DOZZLE_GROUP=label=com.docker.compose.project
      # No filter - shows ALL containers automatically
    mem_limit: 200m
    cpus: 0.5
    labels:
      - "com.docker.compose.project=logs"
      - "dozzle.ignore=true"

  # Log File Writer - Saves logs to files grouped by project
  log-writer:
    image: alpine:latest
    container_name: log-writer
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/logs
    environment:
      - TZ=Asia/Kathmandu
    command: |
      sh -c '
        apk add --no-cache docker-cli jq
        mkdir -p /logs/projects /logs/containers

        while true; do
          echo "📊 Scanning containers at $(date)"

          docker ps --format "{{.Names}}" | while read container; do
            if [ ! -z "$container" ]; then
              project=$(docker inspect "$container" --format "{{index .Config.Labels \"com.docker.compose.project\"}}" 2>/dev/null || echo "unknown")
              mkdir -p "/logs/projects/$project"
              docker logs --tail=500 "$container" > "/logs/projects/$project/${container}.log" 2>&1
            fi
          done

          sleep 300  # 5 minutes
        done
      '
    labels:
      - "com.docker.compose.project=logs"

# No networks needed - using host networking for universal container access
