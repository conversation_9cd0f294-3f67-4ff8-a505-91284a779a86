version: '3.8'

services:
  # Standalone Dozzle for monitoring ALL Docker containers across different compose files
  dozzle-central:
    container_name: dozzle_central_dashboard
    image: amir20/dozzle:latest
    hostname: diwas-central-logs
    restart: unless-stopped
    volumes:
      # Mount Docker socket for container discovery (read-only)
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/hosts:/etc/hosts:ro
    ports:
      # Expose Dozzle on port 8204 (different from your existing 8203)
      - "8204:8080"
    environment:
      # Enable authentication with your preferred password
      - DOZZLE_USERNAME=diwas
      - DOZZLE_PASSWORD=diwas
      # Set hostname for better identification
      - DOZZLE_HOSTNAME=diwas-central-dashboard
      # Show containers with 'diwas' in name (matches your preference)
      - DOZ<PERSON><PERSON>_FILTER=name=diwas
      # Disable analytics
      - DOZZLE_NO_ANALYTICS=true
      # Set log level
      - DOZZLE_LEVEL=info
      # Enable real-time logs with more lines
      - DOZZLE_TAILSIZE=500
      # Enable container actions (start/stop/restart)
      - <PERSON><PERSON><PERSON><PERSON>LE_ENABLE_ACTIONS=true
      # Set base path if behind reverse proxy
      - <PERSON><PERSON><PERSON><PERSON><PERSON>_BASE=/
    # Use host networking to see ALL containers regardless of their networks
    network_mode: host
    mem_limit: 150m
    cpus: 0.3
    labels:
      - "traefik.enable=false"  # Disable Traefik routing
      - "dozzle.ignore=true"    # Don't monitor Dozzle itself
      - "project=monitoring"
      - "service=dozzle-central"
      - "environment=all"
      - "description=Central Docker container log dashboard"

# No networks needed - using host networking for universal container access
