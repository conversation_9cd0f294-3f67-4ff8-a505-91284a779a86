---
# Universal Dozzle and Log Management
# Auto-discovers ALL Docker containers with host network
# Groups by project names automatically

version: '3.8'

services:
  # Universal Dozzle - Scans ALL containers automatically
  dozzle:
    image: amir20/dozzle:latest
    container_name: universal-dozzle
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host  # Host network for auto-discovery
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/hosts:/etc/hosts:ro
      - ./logs:/logs
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_NO_ANALYTICS=true
      - DOZZLE_HOSTNAME=${HOSTNAME:-docker-host}
      - DOZZLE_ENABLE_ACTIONS=true
      - DOZZLE_ADDR=0.0.0.0:8200
      - DOZZLE_TAILSIZE=1000
      # Group by both folder name and project name: "echo_bot (eko)"
      - DOZZLE_GROUP=label=com.docker.compose.project
      # No filter - shows ALL containers automatically
    mem_limit: 200m
    cpus: 0.5
    labels:
      - "com.docker.compose.project=logs"
      - "dozzle.ignore=true"

  # Log File Writer - Saves logs to files grouped by project
  log-writer:
    image: alpine:latest
    container_name: log-writer
    hostname: ${HOSTNAME:-docker-host}
    restart: unless-stopped
    network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/logs
    environment:
      - TZ=Asia/Kathmandu
    command: |
      sh -c '
        apk add --no-cache docker-cli jq
        mkdir -p /logs/projects /logs/containers

        # Function to collect logs from any container (running or stopped)
        collect_container_logs() {
          local container="$1"
          local status="$2"

          if [ ! -z "$container" ]; then
            # Get Docker Compose project (folder name like "echo_bot")
            compose_project=$(docker inspect "$container" --format "{{index .Config.Labels \"com.docker.compose.project\"}}" 2>/dev/null || echo "standalone")

            # For containers from our main app, check if they have our custom project intent
            if echo "$container" | grep -E "(traefik|eko-api)" > /dev/null; then
              folder_name="${compose_project} (eko)"
            else
              folder_name="$compose_project"
            fi

            # Handle edge cases
            if [ "$compose_project" = "<no value>" ] || [ -z "$compose_project" ]; then
              folder_name=$(echo "$container" | cut -d"-" -f1 2>/dev/null || echo "standalone")
            fi

            mkdir -p "/logs/projects/$folder_name"

            # Get ALL logs (not just tail) for persistent storage
            docker logs "$container" > "/logs/projects/$folder_name/${container}.log" 2>&1

            # Add timestamp and status info at the end
            echo "" >> "/logs/projects/$folder_name/${container}.log"
            echo "=== Log collected at $(date) - Container Status: $status ===" >> "/logs/projects/$folder_name/${container}.log"

            echo "📝 Logged $container -> $folder_name [$status]"
          fi
        }

        while true; do
          echo "📊 Scanning ALL containers (running + stopped) at $(date)"

          # Collect logs from RUNNING containers
          docker ps --format "{{.Names}}" | while read container; do
            collect_container_logs "$container" "RUNNING"
          done

          # Collect logs from STOPPED/EXITED containers (last 48 hours)
          docker ps -a --filter "status=exited" --filter "since=48h" --format "{{.Names}}" | while read container; do
            collect_container_logs "$container" "STOPPED"
          done

          # Also check containers that died/crashed
          docker ps -a --filter "status=dead" --filter "since=48h" --format "{{.Names}}" | while read container; do
            collect_container_logs "$container" "CRASHED"
          done

          # Create summary of all containers
          echo "=== Container Status Summary - $(date) ===" > /logs/container_summary.txt
          echo "RUNNING CONTAINERS:" >> /logs/container_summary.txt
          docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" >> /logs/container_summary.txt
          echo "" >> /logs/container_summary.txt
          echo "STOPPED CONTAINERS (last 48h):" >> /logs/container_summary.txt
          docker ps -a --filter "status=exited" --filter "since=48h" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" >> /logs/container_summary.txt

          sleep 300  # 5 minutes
        done
      '
    labels:
      - "com.docker.compose.project=logs"

# No networks needed - using host networking for universal container access
