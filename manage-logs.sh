#!/bin/bash

# Standardized Docker Log Management Script
# Manages universal Docker container log scanning and display

set -e

COMPOSE_FILE="docker-compose-logs.yml"
LOGS_DIR="./logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running or not accessible"
        exit 1
    fi
}

# Function to create logs directory
setup_logs_dir() {
    print_status "Setting up logs directory..."
    mkdir -p "$LOGS_DIR/containers"
    mkdir -p "$LOGS_DIR/projects"
    chmod 755 "$LOGS_DIR"
    print_status "Logs directory created at: $LOGS_DIR"
}

# Function to start the log system
start_logs() {
    print_header "Starting Universal Docker Log System"
    check_docker
    setup_logs_dir
    
    print_status "Starting Dozzle and Log Collector..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    print_status "Waiting for services to start..."
    sleep 5
    
    print_status "Log system started successfully!"
    print_status "🌐 Dozzle Web Interface: http://localhost:8080"
    print_status "📁 Log Files Location: $LOGS_DIR"
    
    # Show running containers
    echo ""
    print_header "Log System Status"
    docker-compose -f "$COMPOSE_FILE" ps
}

# Function to stop the log system
stop_logs() {
    print_header "Stopping Universal Docker Log System"
    docker-compose -f "$COMPOSE_FILE" down
    print_status "Log system stopped"
}

# Function to restart the log system
restart_logs() {
    print_header "Restarting Universal Docker Log System"
    stop_logs
    sleep 2
    start_logs
}

# Function to show logs
show_logs() {
    print_header "Log System Container Logs"
    docker-compose -f "$COMPOSE_FILE" logs -f
}

# Function to show status
status() {
    print_header "Universal Docker Log System Status"
    
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        print_status "Log system is running"
        echo ""
        print_status "🌐 Dozzle Web Interface: http://localhost:8080"
        print_status "📁 Log Files: $LOGS_DIR"
        
        echo ""
        print_header "Service Status"
        docker-compose -f "$COMPOSE_FILE" ps
        
        echo ""
        print_header "Discovered Containers"
        docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
        
        echo ""
        print_header "Available Log Files"
        if [ -d "$LOGS_DIR/projects" ]; then
            find "$LOGS_DIR/projects" -name "*.log" -type f | head -10
        fi
    else
        print_warning "Log system is not running"
        print_status "Use './manage-logs.sh start' to start the system"
    fi
}

# Function to clean old logs
clean_logs() {
    print_header "Cleaning Old Log Files"
    if [ -d "$LOGS_DIR" ]; then
        print_status "Removing old log files..."
        rm -rf "$LOGS_DIR"/*
        print_status "Log files cleaned"
    else
        print_warning "No logs directory found"
    fi
}

# Function to open Dozzle in browser
open_dozzle() {
    print_status "Opening Dozzle web interface..."
    if command -v xdg-open > /dev/null; then
        xdg-open http://localhost:8080
    elif command -v open > /dev/null; then
        open http://localhost:8080
    else
        print_status "Please open http://localhost:8080 in your browser"
    fi
}

# Main script logic
case "${1:-}" in
    start)
        start_logs
        ;;
    stop)
        stop_logs
        ;;
    restart)
        restart_logs
        ;;
    logs)
        show_logs
        ;;
    status)
        status
        ;;
    clean)
        clean_logs
        ;;
    open)
        open_dozzle
        ;;
    *)
        print_header "Universal Docker Log Management"
        echo "Usage: $0 {start|stop|restart|logs|status|clean|open}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the universal log system (Dozzle + Log Collector)"
        echo "  stop    - Stop the log system"
        echo "  restart - Restart the log system"
        echo "  logs    - Show log system container logs"
        echo "  status  - Show system status and discovered containers"
        echo "  clean   - Clean old log files"
        echo "  open    - Open Dozzle web interface in browser"
        echo ""
        echo "Features:"
        echo "  🔍 Automatically scans ALL Docker containers"
        echo "  🌐 Web interface at http://localhost:8080"
        echo "  📁 Saves logs to files in ./logs directory"
        echo "  🏷️  Groups containers by project labels"
        echo "  🔄 Updates every 5 minutes"
        exit 1
        ;;
esac
